export function cleanHtmlTags(text: string): string {
  if (!text) return '';
  return text
    // 将HTML标签替换为空格
    .replace(/<[^>]+>/g, ' ')
    // 将 &nbsp; 替换为空格
    .replace(/&nbsp;/g, ' ')
    // 清理每行前后的空格
    .split('\n')
    .map((line) => line.trim())
    .join('\n')
    // 合并多个空格为一个
    .replace(/[ \t]+/g, ' ')
    // 移除标点符号前的空格
    .replace(/\s+([,.!?;:])/g, '$1')
    // 移除空行
    .replace(/\n+/g, '\n')
    // 去掉首尾的空格和换行
    .trim();
}
